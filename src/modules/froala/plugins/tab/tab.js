/**
 * Tabs Plugin for Froala Editor
 * Allows inserting tabs into the editor content
 */
import "./tab.css";
import FroalaEditor from "froala-editor";

const tabs = function (editor) {
  // Private variables
  let _editor = editor;

  // Add popup templates
  // Instead of using Ext.merge, we'll directly extend the POPUP_TEMPLATES object
  FroalaEditor.POPUP_TEMPLATES["tabs.insert"] = "[_BUTTONS_][_ROWS_COLUMNS_]";
  FroalaEditor.POPUP_TEMPLATES["tabs.edit"] = "[_BUTTONS_]";
  FroalaEditor.POPUP_TEMPLATES["tabs.colors"] =
    "[_BUTTONS_][_COLORS_][_CUSTOM_COLOR_]";

  /**
   * Hover cell in the tab insertion grid
   */
  function _hoverCell($table_cell) {
    const row = $table_cell.data("row");
    const col = $table_cell.data("col");
    const $select_size = $table_cell.parent();

    // Update size in title
    $select_size.siblings(".fr-table-size-info").html(col + " Tab(s)");

    // Remove hover and fr-active-item class from all cells
    $select_size.find("> span").removeClass("hover fr-active-item");

    // Add hover class only to the correct cells
    for (let i = 1; i <= _editor.opts.tabsInsertMaxSizeRows; i++) {
      for (let j = 0; j <= _editor.opts.tabsInsertMaxSizeColumns; j++) {
        const $cell = $select_size.find(
          '> span[data-row="' + i + '"][data-col="' + j + '"]'
        );

        if (i <= row && j <= col) {
          $cell.addClass("hover");
        } else if (i <= row + 1 || (i <= 2 && !_editor.helpers.isMobile())) {
          $cell.css("display", "inline-block");
        } else if (i > 2 && !_editor.helpers.isMobile()) {
          $cell.css("display", "none");
        }
      }
    }

    // Mark table cell as the active item
    $table_cell.addClass("fr-active-item");
  }

  /**
   * Init the insert tabs popup
   */
  function _initInsertPopup(delayed) {
    if (delayed) {
      _editor.popups.onHide("tabs.insert", function () {
        // Clear previous cell selection
        _editor.popups
          .get("tabs.insert")
          .find(
            '.fr-table-size .fr-select-table-size > span[data-row="1"][data-col="1"]'
          )
          .trigger("mouseenter");
      });

      return true;
    }

    // Table buttons
    let table_buttons = "";

    if (
      _editor.opts.tableInsertButtons &&
      _editor.opts.tableInsertButtons.length > 0
    ) {
      table_buttons =
        '<div class="fr-buttons">' +
        _editor.button.buildList(_editor.opts.tableInsertButtons) +
        "</div>";
    }

    const $popup = _editor.popups.create("tabs.insert", {
      buttons: table_buttons,
      rows_columns: _insertTabsHtml(),
    });

    // Initialize insert table grid events
    _editor.events.$on(
      $popup,
      "mouseover",
      ".fr-table-size .fr-select-table-size .fr-table-cell",
      function (e) {
        _hoverCell(_editor.$(e.currentTarget));
      },
      true
    );

    _addAccessibility($popup);

    return $popup;
  }

  /**
   * The HTML for insert tabs grid
   */
  function _insertTabsHtml() {
    // Grid html
    let rows_columns =
      '<div class="fr-table-size"><div class="fr-table-size-info">1 Tab</div><div class="fr-select-table-size">';

    for (let i = 1; i <= _editor.opts.tabsInsertMaxSizeRows; i++) {
      for (let j = 1; j <= _editor.opts.tabsInsertMaxSizeColumns; j++) {
        let display = "inline-block";
        let cls = "fr-table-cell ";

        // Display only first 2 rows
        if (i > 2 && !_editor.helpers.isMobile()) {
          display = "none";
        }

        if (i === 1 && j === 1) {
          cls += " hover";
        }

        rows_columns +=
          '<span class="fr-command ' +
          cls +
          '" tabIndex="-1" data-cmd="tabsInsert" data-row="' +
          i +
          '" data-col="' +
          j +
          '" data-param1="' +
          i +
          '" data-param2="' +
          j +
          '" style="display: ' +
          display +
          ';" role="button"><span></span><span class="fr-sr-only">' +
          i +
          " &times; " +
          j +
          "&nbsp;&nbsp;&nbsp;</span></span>";
      }
      rows_columns += '<div class="new-line"></div>';
    }

    rows_columns += "</div></div>";

    return rows_columns;
  }

  /**
   * Register keyboard events for accessibility
   */
  function _addAccessibility($popup) {
    // Hover cell when tabs.insert cells are focused
    _editor.events.$on($popup, "focus", "[tabIndex]", function (e) {
      _hoverCell(_editor.$(e.currentTarget));
    });

    // Register popup event
    _editor.events.on(
      "popup.tab",
      function (e) {
        const $focused_item = _editor.$(e.currentTarget);
        const key_code = e.which;
        let status;

        // Skip if popup is not visible or focus is elsewhere
        if (
          !_editor.popups.isVisible("tabs.insert") ||
          !$focused_item.is("span, a")
        ) {
          return true;
        }

        if (
          [
            FroalaEditor.KEYCODE.ARROW_UP,
            FroalaEditor.KEYCODE.ARROW_DOWN,
            FroalaEditor.KEYCODE.ARROW_LEFT,
            FroalaEditor.KEYCODE.ARROW_RIGHT,
          ].includes(key_code)
        ) {
          if ($focused_item.is("span.fr-table-cell")) {
            // Get all current cells
            const $cells = $focused_item.parent().find("span.fr-table-cell");
            // Get focused item position
            const index = $cells.index($focused_item);
            // Get cell matrix dimensions
            const columns = _editor.opts.tabsInsertMaxSizeColumns;
            // Get focused item coordinates
            let column = index % columns;
            let line = Math.floor(index / columns);

            // Calculate next coordinates based on key press
            switch (key_code) {
              case FroalaEditor.KEYCODE.ARROW_UP:
                line = Math.max(0, line - 1);
                break;
              case FroalaEditor.KEYCODE.ARROW_DOWN:
                line = Math.min(
                  _editor.opts.tabsInsertMaxSizeRows - 1,
                  line + 1
                );
                break;
              case FroalaEditor.KEYCODE.ARROW_LEFT:
                column = Math.max(0, column - 1);
                break;
              case FroalaEditor.KEYCODE.ARROW_RIGHT:
                column = Math.min(
                  _editor.opts.tabsInsertMaxSizeColumns - 1,
                  column + 1
                );
                break;
            }

            // Get the next element based on the new coordinates
            const nextIndex = line * columns + column;
            const $el = _editor.$($cells.get(nextIndex));

            // Hover cell
            _hoverCell($el);

            // Focus
            _editor.events.disableBlur();
            $el.focus();

            status = false;
          }
        } else if (key_code === FroalaEditor.KEYCODE.ENTER) {
          _editor.button.exec($focused_item);
          status = false;
        }

        // Prevent propagation
        if (status === false) {
          e.preventDefault();
          e.stopPropagation();
        }

        return status;
      },
      true
    );
  }

  /**
   * Show the insert tabs popup
   */
  function _showInsertPopup() {
    const $btn = _editor.$tb.find('.fr-command[data-cmd="insertTabs"]');
    let $popup = _editor.popups.get("tabs.insert");

    if (!$popup) {
      $popup = _initInsertPopup();
    }

    if (!$popup.hasClass("fr-active")) {
      // Insert table popup
      _editor.popups.refresh("tabs.insert");
      _editor.popups.setContainer("tabs.insert", _editor.$tb);

      // Insert table left and top position
      const left = $btn.offset().left + $btn.outerWidth() / 2;
      const top =
        $btn.offset().top +
        (_editor.opts.toolbarBottom ? 10 : $btn.outerHeight() - 10);

      _editor.popups.show("tabs.insert", left, top, $btn.outerHeight());
    }
  }

  /**
   * Insert tabs method - creates tabs HTML and inserts it
   */
  function insertTabs(rows, cols) {
    const tabs = _editor.el.getElementsByClassName("tab-title").length;
    const totalCols = cols || 6;
    const colWidth = (100 / totalCols).toFixed(4);

    let tableHtml = '<table class="tabs-panel" style="width: 100%; border-collapse: collapse; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden; margin: 16px 0;"><tbody>';

    let count = tabs + 1;

    // Row 1: tab titles
    tableHtml += '<tr class="tabs" data-tab>';
    const countList = []; // Track used counts
    for (let col = 0; col < totalCols; col++) {
      const isFirst = col === 0;
      const isLast = col === totalCols - 1;
      const borderRadius = isFirst ? 'border-top-left-radius: 8px;' : isLast ? 'border-top-right-radius: 8px;' : '';

      tableHtml += `
        <td class="tab-title content-text ${count}" style="
          width: ${colWidth}%;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          text-align: center;
          font-weight: 600;
          padding: 14px 20px 12px 20px;
          border-right: ${isLast ? 'none' : '1px solid #dee2e6'};
          color: #495057;
          font-size: 14px;
          letter-spacing: 0.5px;
          transition: all 0.2s ease;
          cursor: pointer;
          position: relative;
          ${borderRadius}
        " contenteditable="true" onmouseover="this.style.background='linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'" onmouseout="this.style.background='linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
          Place pointer here and edit text for Tab ${count}
        </td>`;
      countList.push(count); // Store for matching panel row
      count++;
    }
    tableHtml += '</tr>';

    // Row 2: content for each tab
    tableHtml += '<tr class="tabs-content">';
    for (let col = 0; col < totalCols; col++) {
      const panelCount = countList[col];
      const isFirst = col === 0;
      const isLast = col === totalCols - 1;
      const borderRadius = isFirst ? 'border-bottom-left-radius: 8px;' : isLast ? 'border-bottom-right-radius: 8px;' : '';

      tableHtml += `
        <td class="content" id="panel${panelCount}" style="
          width: ${colWidth}%;
          background-color: #ffffff;
          padding: 24px 20px;
          border-right: ${isLast ? 'none' : '1px solid #dee2e6'};
          border-top: 2px solid #007bff;
          color: #212529;
          font-size: 14px;
          line-height: 1.6;
          min-height: 120px;
          vertical-align: top;
          ${borderRadius}
        " contenteditable="true">
          Update this content for Tab ${panelCount}
        </td>`;
    }
    tableHtml += '</tr>';

    tableHtml += '</tbody></table>';

    // Insert at selection
    let anchorNode;
    if (
      _editor.selection.get().anchorNode &&
      _editor.selection.get().anchorNode.nodeType === Node.TEXT_NODE
    ) {
      anchorNode = _editor.selection.get().anchorNode.parentElement;
    } else {
      anchorNode = _editor.selection.get().anchorNode;
    }

    _editor.html.insert(tableHtml);
  }

  function onInsertTabCallback() {
    if (!this.popups.isVisible("tabs.insert")) {
      _showInsertPopup();
    } else {
      if (this.$el.find(".fr-marker").length) {
        this.events.disableBlur();
        this.selection.restore();
      }
      this.popups.hide("tabs.insert");
    }
  }

  /**
   * Initialize the plugin
   */
  function _init() {
    // Define the icon for tab removal
    FroalaEditor.DefineIcon("tabRemove", {
      NAME: "trash",
      SVG_KEY: "removeTable",
    });

    // Register deletion popup for tabs
    FroalaEditor.POPUP_TEMPLATES["tabs.deletion"] = "[_BUTTONS_]";

    // Create deletion popup with the icon
    _editor.popups.create("tabs.deletion", {
      buttons:
        '<div class="fr-buttons">' +
        '<button class="fr-command removeBtn" data-cmd="removeDiv" title="Delete" type="button">' +
        '<i style="color: #626363;" class="icon icon-16 icon-trash"></i>' +
        '<span class="fr-sr-only">Delete</span>' +
        "</button>" +
        "</div>",
    });
  }

  /**
   * Public plugin methods
   */
  return {
    _init: _init,
    onInsertTabsCallback: onInsertTabCallback,
    showInsertPopup: _showInsertPopup,
    insertTabs: insertTabs,
  };
};

FroalaEditor.PLUGINS.tabs = tabs;

FroalaEditor.DEFAULTS.tabsInsertMaxSizeRows = 1;
FroalaEditor.DEFAULTS.tabsInsertMaxSizeColumns = 5;

FroalaEditor.RegisterCommand("insertTabs", {
  title: "Insert Tabs",
  undo: false,
  focus: true,
  refreshOnCallback: false,
  popup: true,
  callback: function () {
    this.tabs.onInsertTabsCallback.call(this);
  },
});

FroalaEditor.RegisterCommand("tabsInsert", {
  callback: function (cmd, rows, cols) {
    this.tabs.insertTabs.call(this, rows, cols);
    this.popups.hide("tabs.insert");
  },
});

export default tabs;
