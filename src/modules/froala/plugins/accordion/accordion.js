import FroalaEditor from "froala-editor";
import "./accordion.css";

const accordion = function (editor) {
  let _editor = editor;

  FroalaEditor.POPUP_TEMPLATES["accordion.insert"] =
    "[_BUTTONS_][_ROWS_COLUMNS_]";
  FroalaEditor.POPUP_TEMPLATES["accordion.edit"] = "[_BUTTONS_]";
  FroalaEditor.POPUP_TEMPLATES["accordion.colors"] =
    "[_BUTTONS_][_COLORS_][_CUSTOM_COLOR_]";

  function _hoverCell($table_cell) {
    const row = $table_cell.data("row");
    const col = $table_cell.data("col");
    const $select_size = $table_cell.parent();

    // Update size in title.
    $select_size.siblings(".fr-table-size-info").html(row + " Accordion(s)");

    // Remove hover and fr-active-item class from all cells.
    $select_size.find("> span").removeClass("hover fr-active-item");

    // Add hover class only to the correct cells.
    for (let i = 1; i <= editor.opts.accordionInsertMaxSizeRows; i++) {
      for (let j = 0; j <= editor.opts.accordionInsertMaxSizeColumns; j++) {
        const $cell = $select_size.find(
          '> span[data-row="' + i + '"][data-col="' + j + '"]'
        );

        if (i <= row && j <= col) {
          $cell.addClass("hover");
        } else if (i <= row + 1 || (i <= 2 && !editor.helpers.isMobile())) {
          $cell.css("display", "inline-block");
        } else if (i > 2 && !editor.helpers.isMobile()) {
          $cell.css("display", "none");
        }
      }
    }

    // Mark table cell as the active item.
    $table_cell.addClass("fr-active-item");
  }

  function _initInsertPopup(delayed) {
    if (delayed) {
      editor.popups.onHide("accordion.insert", function () {
        // Clear previous cell selection.
        editor.popups
          .get("accordion.insert")
          .find(
            '.fr-table-size .fr-select-table-size > span[data-row="1"][data-col="1"]'
          )
          .trigger("mouseenter");
      });

      return true;
    }

    // Table buttons.
    let table_buttons = "";

    if (
      _editor.opts.tableInsertButtons &&
      _editor.opts.tableInsertButtons.length > 0
    ) {
      table_buttons =
        '<div class="fr-buttons">' +
        editor.button.buildList(editor.opts.tableInsertButtons) +
        "</div>";
    }

    const $popup = editor.popups.create("accordion.insert", {
      buttons: table_buttons,
      rows_columns: _insertAccordionHtml(),
    });

    // Initialize insert table grid events.
    editor.events.$on(
      $popup,
      "mouseover",
      ".fr-table-size .fr-select-table-size .fr-table-cell",
      function (e) {
        _hoverCell(editor.$(e.currentTarget));
      },
      true
    );

    _addAccessibility($popup);

    return $popup;
  }

  function _insertAccordionHtml() {
    // Grid html
    let rows_columns =
      '<div class="fr-table-size"><div class="fr-table-size-info">1 Accordion(s)</div><div class="fr-select-table-size">';

    for (let i = 1; i <= editor.opts.accordionInsertMaxSizeRows; i++) {
      for (let j = 1; j <= editor.opts.accordionInsertMaxSizeColumns; j++) {
        let display = "inline-block";
        let cls = "fr-table-cell ";

        // Display only first 2 rows.
        if (i > 2 && !editor.helpers.isMobile()) {
          display = "none";
        }

        if (i === 1 && j === 1) {
          cls += " hover";
        }

        rows_columns +=
          '<span class="fr-command ' +
          cls +
          '" tabIndex="-1" data-cmd="accordionInsert" data-row="' +
          i +
          '" data-col="' +
          j +
          '" data-param1="' +
          i +
          '" data-param2="' +
          j +
          '" style="display: ' +
          display +
          ';" role="button"><span></span><span class="fr-sr-only">' +
          i +
          " &times; " +
          j +
          "&nbsp;&nbsp;&nbsp;</span></span>";
      }
      rows_columns += '<div class="new-line"></div>';
    }

    rows_columns += "</div></div>";

    return rows_columns;
  }

  function _addAccessibility($popup) {
    // Hover cell when accordion.insert cells are focused.
    editor.events.$on($popup, "focus", "[tabIndex]", function (e) {
      _hoverCell(editor.$(e.currentTarget));
    });

    // Register popup event.
    editor.events.on(
      "popup.tab",
      function (e) {
        const $focused_item = editor.$(e.currentTarget);
        const key_code = e.which;
        let status;

        // Skip if popup is not visible or focus is elsewhere.
        if (
          !editor.popups.isVisible("accordion.insert") ||
          !$focused_item.is("span, a")
        ) {
          return true;
        }

        if (
          [
            FroalaEditor.KEYCODE.ARROW_UP,
            FroalaEditor.KEYCODE.ARROW_DOWN,
            FroalaEditor.KEYCODE.ARROW_LEFT,
            FroalaEditor.KEYCODE.ARROW_RIGHT,
          ].includes(key_code)
        ) {
          if ($focused_item.is("span.fr-table-cell")) {
            // Get all current cells.
            const $cells = $focused_item.parent().find("span.fr-table-cell");
            // Get focused item position.
            const index = $cells.index($focused_item);
            // Get cell matrix dimensions.
            const columns = editor.opts.accordionInsertMaxSizeColumns;
            // Get focused item coordinates.
            let column = index % columns;
            let line = Math.floor(index / columns);

            switch (key_code) {
              case FroalaEditor.KEYCODE.ARROW_UP:
                line = Math.max(0, line - 1);
                break;
              case FroalaEditor.KEYCODE.ARROW_DOWN:
                line = Math.min(
                  editor.opts.accordionInsertMaxSizeRows - 1,
                  line + 1
                );
                break;
              case FroalaEditor.KEYCODE.ARROW_LEFT:
                column = Math.max(0, column - 1);
                break;
              case FroalaEditor.KEYCODE.ARROW_RIGHT:
                column = Math.min(
                  editor.opts.accordionInsertMaxSizeColumns - 1,
                  column + 1
                );
                break;
            }

            // Get the next element based on the new coordinates.
            const nextIndex = line * columns + column;
            const $el = editor.$($cells.get(nextIndex));

            // Hover cell
            _hoverCell($el);

            // Focus.
            editor.events.disableBlur();
            $el.focus();

            status = false;
          }
        } else if (key_code === FroalaEditor.KEYCODE.ENTER) {
          editor.button.exec($focused_item);
          status = false;
        }

        // Prevent propagation.
        if (status === false) {
          e.preventDefault();
          e.stopPropagation();
        }

        return status;
      },
      true
    );
  }

  function _showInsertPopup() {
    const $btn = editor.$tb.find('.fr-command[data-cmd="insertAccordion"]');
    let $popup = editor.popups.get("accordion.insert");

    if (!$popup) {
      $popup = _initInsertPopup();
    }

    if (!$popup.hasClass("fr-active")) {
      // Insert table popup
      editor.popups.refresh("accordion.insert");
      editor.popups.setContainer("accordion.insert", editor.$tb);

      // Insert table left and top position.
      const left = $btn.offset().left + $btn.outerWidth() / 2;
      const top =
        $btn.offset().top +
        (editor.opts.toolbarBottom ? 10 : $btn.outerHeight() - 10);

      editor.popups.show("accordion.insert", left, top, $btn.outerHeight());
    }
  }

  function insertAccordion(rows) {
    const accordions = editor.el.getElementsByTagName("dd").length;
    let count = accordions + 1;

    let tableHtml = '<table class="accordion-panel" style="width: 100%; border-collapse: collapse; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden; margin: 16px 0;"><tbody class="accordion-navigation">';

    for (let i = 1; i <= rows; i++) {
      const isFirst = i === 1;
      const isLast = i === rows;
      const headerBorderRadius = isFirst ? 'border-top-left-radius: 8px; border-top-right-radius: 8px;' : '';
      const contentBorderRadius = isLast ? 'border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;' : '';

      // Header row
      tableHtml += `
        <tr class="accordion">
          <td class="accordion-header" href="#panel${count}" style="
            width: 100%;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 16px 20px;
            font-weight: 600;
            color: #495057;
            font-size: 14px;
            letter-spacing: 0.5px;
            border-bottom: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            ${headerBorderRadius}
          " contenteditable="true" onmouseover="this.style.background='linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%)'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'" onmouseout="this.style.background='linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <span style="display: inline-block; margin-right: 12px; font-size: 12px; color: #6c757d; transition: transform 0.2s ease;">▶</span>Place pointer here and edit text for Accordion ${count}
          </td>
        </tr>
      `;

      // Content row
      tableHtml += `
        <tr>
          <td id="panel${count}" class="content" style="
            width: 100%;
            background-color: #ffffff;
            padding: 24px 20px;
            color: #212529;
            font-size: 14px;
            line-height: 1.6;
            min-height: 80px;
            border-bottom: ${isLast ? 'none' : '1px solid #dee2e6'};
            ${contentBorderRadius}
          " contenteditable="true">
            Update this content for Accordion ${count}
          </td>
        </tr>
      `;

      count++;
    }

    tableHtml += '</tbody></table>';

    // Insert at current position
    let anchorNode;

    if (
      editor.selection.get().anchorNode &&
      editor.selection.get().anchorNode.nodeType === Node.TEXT_NODE
    ) {
      anchorNode = editor.selection.get().anchorNode.parentElement;
    } else {
      anchorNode = editor.selection.get().anchorNode;
    }

    const liTag = anchorNode && anchorNode.closest("li");
    const ulTag = liTag && liTag.closest("ul, ol");

    // if (liTag && ulTag) {
    //   ulTag.insertAdjacentHTML("afterend", tableHtml);
    // } else {
      editor.html.insert(tableHtml);
    // }
  }

  function onInsertAccordionCallback() {
    if (!this.popups.isVisible("accordion.insert")) {
      _showInsertPopup();
    } else {
      if (this.$el.find(".fr-marker").length) {
        this.events.disableBlur();
        this.selection.restore();
      }
      this.popups.hide("accordion.insert");
    }
  }

  function _init() {
    // Register deletion popup for accordions
    FroalaEditor.POPUP_TEMPLATES["accordion.deletion"] = "[_BUTTONS_]";

    // Create deletion popup
    _editor.popups.create("accordion.deletion", {
      buttons:
        '<div class="fr-buttons"><button class="fr-command removeBtn" data-cmd="removeDiv" title="Delete" type="button"><i style="color: #626363;" class="icon icon-16 icon-trash"></i></button></div>',
    });
  }

  return {
    _init: _init,
    onInsertAccordionsCallback: onInsertAccordionCallback,
    showInsertPopup: _showInsertPopup,
    insertAccordion: insertAccordion,
  };
};

FroalaEditor.PLUGINS.accordion = accordion;

FroalaEditor.DEFAULTS.accordionInsertMaxSizeRows = 1;
FroalaEditor.DEFAULTS.accordionInsertMaxSizeColumns = 1;

FroalaEditor.RegisterCommand("insertAccordion", {
  title: "Insert Accordion",
  undo: false,
  focus: true,
  refreshOnCallback: false,
  popup: true,
  callback: function () {
    this.accordion.onInsertAccordionsCallback.call(this);
  },
});

FroalaEditor.RegisterCommand("accordionInsert", {
  callback: function (cmd, rows, cols) {
    this.accordion.insertAccordion.call(this, rows, cols);
    this.popups.hide("accordion.insert");
  },
});

export default accordion;
